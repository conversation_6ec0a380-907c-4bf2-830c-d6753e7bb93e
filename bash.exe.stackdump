Stack trace:
Frame         Function      Args
0007FFFF9960  00021006116E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8860) msys-2.0.dll+0x2116E
0007FFFF9960  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFF9960  0002100469F2 (00021028DF99, 0007FFFF9818, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9960  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFF9960  00021006A525 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDF1E20000 ntdll.dll
7FFDF03F0000 KERNEL32.DLL
7FFDEF7A0000 KERNELBASE.dll
7FFDEFCD0000 USER32.dll
7FFDEF410000 win32u.dll
7FFDF03C0000 GDI32.dll
7FFDEEF40000 gdi32full.dll
7FFDEF440000 msvcp_win.dll
7FFDEF140000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDEFFA0000 advapi32.dll
7FFDF0310000 msvcrt.dll
7FFDEFBA0000 sechost.dll
7FFDF01E0000 RPCRT4.dll
7FFDEE510000 CRYPTBASE.DLL
7FFDEF700000 bcryptPrimitives.dll
7FFDF0D90000 IMM32.DLL
