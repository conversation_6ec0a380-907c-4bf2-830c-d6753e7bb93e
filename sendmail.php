<?php
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $to = "<EMAIL>";
    $subject = "Contact Form Submission from AMI Website";
    $name = htmlspecialchars($_POST["username"] ?? "");
    $email = htmlspecialchars($_POST["email"] ?? "");
    $phone = htmlspecialchars($_POST["phone"] ?? "");
    $subject_input = htmlspecialchars($_POST["subject"] ?? "");
    $message = htmlspecialchars($_POST["message"] ?? "");

    $body = "Name: $name\nEmail: $email\nPhone: $phone\nSubject: $subject_input\nMessage:\n$message";
    $headers = "From: $email\r\nReply-To: $email\r\n";

    if (mail($to, $subject, $body, $headers)) {
        echo json_encode(["success" => true]);
    } else {
        echo json_encode(["success" => false]);
    }
} else {
    http_response_code(405);
    echo json_encode(["success" => false]);
}
?>
