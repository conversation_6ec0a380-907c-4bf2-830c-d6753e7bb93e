<!DOCTYPE html>
<html lang="en">


<!-- Mirrored from pixydrops.com/linoorhtml/contact-2.html by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 29 May 2025 16:25:33 GMT -->
<!-- Added by HTTrack -->
<meta http-equiv="content-type" content="text/html;charset=utf-8" /><!-- /Added by HTTrack -->

<head>
    <meta charset="utf-8">
    <title>AMI | Contact</title>
    <!-- Stylesheets -->
    <link rel="preconnect" href="https://fonts.gstatic.com/">
    <link
        href="https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300;0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&amp;family=Teko:wght@300;400;500;600;700&amp;display=swap"
        rel="stylesheet">
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/fontawesome-all.css" rel="stylesheet">
    <link href="css/owl.css" rel="stylesheet">
    <link href="css/flaticon.css" rel="stylesheet">
    <link href="css/linoor-icons-2.css" rel="stylesheet">
    <link href="css/animate.css" rel="stylesheet">
    <link href="css/jquery-ui.css" rel="stylesheet">
    <link href="css/jquery.fancybox.min.css" rel="stylesheet">
    <link href="css/hover.css" rel="stylesheet">
    <link href="css/custom-animate.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
    <!-- rtl css -->
    <link href="css/rtl.css" rel="stylesheet">
    <!-- Responsive File -->
    <link href="css/responsive.css" rel="stylesheet">

    <!-- Color css -->
    <link rel="stylesheet" id="jssDefault" href="css/colors/color-default.css">

    <!-- <link rel="shortcut icon" href="images/favicon.png" id="fav-shortcut" type="image/x-icon">
    <link rel="icon" href="images/favicon.png" id="fav-icon" type="image/x-icon"> -->

    <!-- Responsive Settings -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <!--[if lt IE 9]><script src="https://cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv.js"></script><![endif]-->
    <!--[if lt IE 9]><script src="js/respond.js"></script><![endif]-->
    </head>

<body>

    <div class="page-wrapper">

        <!-- Preloader -->
        <div class="preloader">
        <style>
          .preloader h1 {
            color: white;
            font-size: 50px;
            text-align: center;
            margin-top: 20%;
          }
        </style>
        <h1 color="white">Advent Mission Institute</h1>
      </div>

<!-- Main Header -->
        <header class="main-header header-style-one">
            <!-- Header Upper -->
            <div class="header-upper">
                <div class="inner-container clearfix">
                    <!--Logo-->
                    <div class="logo-box">
                        <div class="logo">
                            <a href="index.html" title="Linoor - DIgital Agency HTML Template">
                                <img src="images/logo-main.png" id="thm-logo"
                                    alt="Linoor - DIgital Agency HTML Template"
                                    title="AMI Logo Advent Mission Institute">
                                <h1>
                                    Advent Mission <br />
                                    <span>Institute</span>
                                </h1>
                            </a>
                        </div>
                    </div>
                    <div class="nav-outer clearfix">
                        <!--Mobile Navigation Toggler-->
                        <div class="mobile-nav-toggler">
                            <span class="icon flaticon-menu-2"></span><span class="txt">Menu</span>
                        </div>

                        <!-- Main Menu -->
                        <nav class="main-menu navbar-expand-md navbar-light">
                            <div class="collapse navbar-collapse show clearfix" id="navbarSupportedContent">
                                <ul class="navigation clearfix">
                                    <li>
                                        <a href="index.html">Home</a>
                                    </li>
                                    <li class="dropdown">
                                        <a href="about.html">About Us</a>
                                        <ul>
                                            <li><a href="who.html">Who We Are</a></li>
                                            <li><a href="pastor.html">Pastor</a></li>
                                            <li><a href="team.html">Team</a></li>
                                        </ul>
                                    </li>
                                    <li class="dropdown">
                                        <a href="ministries.html">Ministries</a>
                                        <ul>
                                            <li><a href="evangelism.html">Evangelism</a></li>
                                            <li><a href="church-renewal.html">Church Renewal</a></li>
                                            <li><a href="leadership-training.html">Spiritual Leadership</a></li>
                                            <li><a href="media-ministry.html">Media Ministry</a></li>
                                            <li><a href="education.html">Education</a></li>
                                            <li><a href="specialized-workshops.html">Specialized Workshop</li>
                                            <li><a href="building-mission.html">Building Mission</a></li>
                                            <li><a href="health.html">Medical Health & Wellness</a></li>

                                        </ul>
                                    </li>
                                    <li class="dropdown">
                                        <a href="masterplan-overview.html">Campus Vision</a>
                                        <ul>
                                            <li><a href="masterplan-overview.html">Overview</a></li>
                                            <li><a href="masterplan-event-center.html">Event Center</a></li>
                                            <li><a href="masterplan-k8-forest-school.html">K-8 Nature School</a></li>
                                            <li><a href="masterplan-lifestyle-health.html">Lifestyle Health</a></li>
                                            <li><a href="masterplan-admin.html">Admin</a></li>
                                            <li><a href="masterplan-9-12-forest-school.html">9-12 Nature School</a></li>
                                            <li><a href="masterplan-media.html">Media Minsitry</a></li>
                                        </ul>
                                    </li>
                                    <li>
                                        <a href="https://www.youtube.com/@pastor-ronkelly" target="_blank">Sermons</a>
                                    </li>
                                    <li>
                                        <a href="schedule.html">Schedule</a>
                                    </li>
                                    <li>
                                        <a href="contact.html">Contact</a>
                                    </li>
                                    <li class="give-link">
                                        <a href="give.html">Give</a>
                                    </li>
                                </ul>
                            </div>
                        </nav>
                    </div>

                    <div class="other-links clearfix">
                        <!-- cart btn -->
                        <!-- <div class="cart-btn">
                            <a href="cart.html" class="theme-btn cart-toggler"><span
                                    class="flaticon-shopping-cart"></span></a>
                        </div> -->
                        <!--Search Btn-->
                        <!-- <div class="search-btn">
                            <button type="button" class="theme-btn search-toggler"><span
                                    class="flaticon-loupe"></span></button>
                        </div> -->
                        <div class="link-box">
                            <a class="theme-btn btn-style-one give" href="give.html">
                                <i class="btn-curve"></i>
                                <span class="btn-title">Give</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <!--End Header Upper-->
        </header>
        <!-- End Main Header -->

        <!--Mobile Menu-->
        <div class="side-menu__block">
            <div class="side-menu__block-overlay custom-cursor__overlay">
                <div class="cursor"></div>
                <div class="cursor-follower"></div>
            </div>
            <!-- /.side-menu__block-overlay -->
            <div class="side-menu__block-inner">
                <div class="side-menu__top justify-content-end">
                    <a href="#" class="side-menu__toggler side-menu__close-btn"><img src="images/icons/close-1-1.png"
                            alt="" /></a>
                </div>
                <!-- /.side-menu__top -->

                <nav class="mobile-nav__container">
                    <!-- content is loading via js -->
                </nav>
                <div class="side-menu__sep"></div>
                <!-- /.side-menu__sep -->
                <div class="side-menu__content">
                    <!-- <p>Linoor is a premium Template for Digital Agencies, Start Ups, Small Business and a wide range of
                        other agencies.</p> -->
                    <p>
                        <!-- <a href="mailto:<EMAIL>"><EMAIL></a> -->
                        <br />
                        <!-- <a href="mailto:<EMAIL>"><EMAIL></a> -->
                    </p>
                    <div class="side-menu__social">
                        <a href="https://www.facebook.com/profile.php?id=61573410520121" target="_blank"><i class="fab fa-facebook-square"></i></a>
                        <a href="https://www.youtube.com/@pastor-ronkelly" target="_blank"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <!-- /.side-menu__content -->
            </div>
            <!-- /.side-menu__block-inner -->
        </div>
        <!-- /.side-menu__block -->



        <!-- Banner Section -->
        <section class="page-banner">
            <div class="image-layer" style="background-image:url(https://cdn.prod.website-files.com/65cfc8641cecc667c97dc5dc/661d49dd4fa72f3790ba9177_contactus.jpg);"></div>
            <div class="shape-1"></div>
            <div class="shape-2"></div>
            <div class="banner-inner">
                <div class="auto-container">
                    <div class="inner-container clearfix">
                        <h1>Contact</h1>
                    
                    </div>
                </div>
            </div>
        </section>
        <!--End Banner Section -->

        <!--Contact Section-->
        <section class="contact-section contact-two">
            <div class="auto-container">
                <div class="row">
                    <div class="col-lg-4">
                        <div class="contact-two__content">
                            <div class="sec-title">
                                <h2>Contact Us  </h2>
                            </div>
                            <p class="contact-two__text">Empowering Churches for a Mission-Driven Future</p>
                            <!-- /.contact-two__text -->
                            <div class="contact-two__social">
                                <a href="https://www.facebook.com/profile.php?id=61573410520121" target="_blank" class="fab fa-facebook"></a>
                                <a href="https://www.youtube.com/@pastor-ronkelly" target="_blank" class="fab fa-youtube"></a>
                            </div><!-- /.contact-two__social -->
                        </div><!-- /.contact-two__content -->
                    </div><!-- /.col-lg-4 -->
                    <div class="col-lg-8">
                        <div class="form-box">
                            <div class="default-form">
                                <form method="POST" action="sendmail.php" id="contact-form">
                                    <div class="row clearfix">
                                        <div class="form-group col-lg-6 col-md-6 col-sm-12">
                                            <div class="field-inner">
                                                <input type="text" name="username" value="" placeholder="Your Name"
                                                    required="">
                                            </div>
                                        </div>
                                        <div class="form-group col-lg-6 col-md-6 col-sm-12">
                                            <div class="field-inner">
                                                <input type="email" name="email" value="" placeholder="Email Address"
                                                    required="">
                                            </div>
                                        </div>
                                        <div class="form-group col-lg-6 col-md-6 col-sm-12">
                                            <div class="field-inner">
                                                <input type="tel" name="phone" value="" placeholder="Phone Number">
                                            </div>
                                        </div>
                                        <div class="form-group col-lg-6 col-md-6 col-sm-12">
                                            <div class="field-inner">
                                                <input type="text" name="subject" value="" placeholder="Subject">
                                            </div>
                                        </div>
                                        <div class="form-group col-lg-12 col-md-12 col-sm-12">
                                            <div class="field-inner">
                                                <textarea name="message" placeholder="Write Message"
                                                    required=""></textarea>
                                            </div>
                                        </div>
                                        <div class="form-group col-lg-12 col-md-12 col-sm-12">
                                            <button class="theme-btn btn-style-one" type="submit">
                                                <i class="btn-curve"></i>
                                                <span class="btn-title">Send message</span>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                                <!-- Success Modal -->
                                <div id="successModal" class="ami-modal" style="display:none;">
                                    <div class="ami-modal-overlay"></div>
                                    <div class="ami-modal-content">
                                        <div class="ami-modal-header">
                                            <div class="ami-success-icon">
                                                <i class="fas fa-check-circle"></i>
                                            </div>
                                            <h3>Thank You!</h3>
                                        </div>
                                        <div class="ami-modal-body">
                                            <p>Thank you for contacting us here at AMI. We will get back to you soon.</p>
                                        </div>
                                        <div class="ami-modal-footer">
                                            <button class="ami-btn-close" onclick="closeModal('successModal')">
                                                <i class="fas fa-times"></i>
                                                Close
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Error Modal -->
                                <div id="errorModal" class="ami-modal" style="display:none;">
                                    <div class="ami-modal-overlay"></div>
                                    <div class="ami-modal-content">
                                        <div class="ami-modal-header error">
                                            <div class="ami-error-icon">
                                                <i class="fas fa-exclamation-triangle"></i>
                                            </div>
                                            <h3>Oops!</h3>
                                        </div>
                                        <div class="ami-modal-body">
                                            <p>Something went wrong while sending your message. Please try again.</p>
                                        </div>
                                        <div class="ami-modal-footer">
                                            <button class="ami-btn-retry" onclick="closeModal('errorModal')">
                                                <i class="fas fa-redo"></i>
                                                Retry
                                            </button>
                                            <button class="ami-btn-close" onclick="closeModal('errorModal')">
                                                <i class="fas fa-times"></i>
                                                Close
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div><!-- /.col-lg-8 -->
                </div><!-- /.row -->

            </div>
        </section>


        <section class="contact-info-two">
            <div class="auto-container">
                <div class="row">
                    <div class="col-md-12 col-lg-6">
                        <div class="contact-info-two__card wow fadeInUp" data-wow-duration="1500ms">
                            <i class="fa fa-map-marker-alt"></i>
                            <a href="#">P.O. Box 227<br />Berrien Springs, MI 49103</a>
                        </div>
                    </div>

                    <div class="col-md-12 col-lg-6">
                        <div class="contact-info-two__card wow fadeInUp" data-wow-duration="1500ms">
                            <i class="icon flaticon-call-1"></i>
                            <a href="#">+1 269-421-4331  <br> <br></a>
                        </div>
                    </div>
                    
                    
                </div>
            </div>
        </section>

        <!-- <div class="map-box">
            <iframe class="map-iframe"
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d230899.1642407818!2d145.06327708904033!3d-37.792102974783376!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6ad65cd0db468a97%3A0xb61fde84306fc38a!2sMelbourne%20Zoo!5e0!3m2!1sen!2s!4v1592307685926!5m2!1sen!2s"
                style="border:0;" aria-hidden="false" tabindex="0"></iframe>
        </div> -->

        <!-- Main Footer -->
        <footer class="main-footer">
            <div class="auto-container">
                <!--Widgets Section-->
                <div class="widgets-section">
                    <div class="row clearfix">

                        <div class="column col-xl-3 col-lg-6 col-md-6 col-sm-12">
                            <div class="footer-widget logo-widget">
                                <div class="widget-content">
                                    <div class="logo">
                                        <a href="index.html"><img id="fLogo" src="./images/logo-main.png" alt="" /></a>
                                    </div>
                                    <div class="text">
                                        Advent Mission Institute <br><br>
                                        Equipping leaders. Reviving churches. Preparing the world for Christ's return.
                                    </div>
                                    <ul class="social-links clearfix">
                                        <li>
                                            <a href="https://www.facebook.com/profile.php?id=61573410520121"
                                                target="_blank"><span class="fab fa-facebook-square"></span></a>
                                        </li>
                                        <li>
                                            <a href="https://www.youtube.com/@pastor-ronkelly" target="_blank"><span
                                                    class="fab fa-youtube"></span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>


                        <div class="column col-xl-3 col-lg-6 col-md-6 col-sm-12">
                            <div class="footer-widget links-widget">
                                <div class="widget-content">
                                    <h6>Explore</h6>
                                    <div class="row clearfix">
                                        <div class="col-md-6 col-sm-12">
                                            <ul>
                                                <li><a href="about.html">About</a></li>
                                                <li><a href="ministries.html">Ministry</a></li>
                                                <li><a href="masterplan-overview.html">Campus Vision</a></li>
                                                <li><a href="https://www.youtube.com/@pastor-ronkelly"
                                                        target="_blank">Sermons</a></li>
                                                <li><a href="schedule.html">Schedule</a></li>
                                                <li><a href="contact.html">Contact</a></li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6 col-sm-12">
                                            <ul>
                                                <li><a href="evangelism.html">Evangelism</a></li>
                                                <li><a href="church-renewal.html">Church Renewal</a></li>
                                                <li><a href="health.html">Health</a></li>
                                                <!-- <li><a href="#">Help</a></li> -->
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="column col-xl-3 col-lg-6 col-md-6 col-sm-12">
                            <div class="footer-widget info-widget">
                                <div class="widget-content">
                                    <h6>Contact</h6>
                                    <ul class="contact-info">
                                        <li class="address">
                                            <span class="icon flaticon-pin-1"></span> Advent Mission Institute,
                                            Inc.<br />
                                            P.O. Box 227<br />Berrien Springs, MI 49103
                                        </li>

                                    </ul>
                                </div>
                            </div>
                        </div>


                        <div class="column col-xl-3 col-lg-6 col-md-6 col-sm-12">
                            <div class="footer-widget newsletter-widget">
                                <div class="widget-content">
                                    <h6>Newsletter</h6>
                                    <div class="newsletter-form">
                                        <form method="post" action="https://ami365.us10.list-manage.com/subscribe/post?u=6e206f285c5dba87949052feb&amp;id=ca817a04ae&amp;f_id=007791e3f0">
                                            <div class="form-group clearfix">
                                                <input type="email" name="email" value="" placeholder="Email Address"
                                                    required="" />
                                                <button type="submit" class="theme-btn">
                                                    <span class="fa fa-envelope"></span>
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="text">
                                        Sign up for our latest news & articles.


                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="auto-container">
                     
                    AMI365 is an independent, self-supporting ministry that supports the mission of the Seventh-day
                    Adventist Church. Not owned, operated, or officially affiliated with the General Conference of
                    Seventh Day Adventists.
                    <br><br>
                </div>
            </div>


            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <div class="auto-container">
                    <div class="inner clearfix">
                        <div class="copyright">
                            &copy; Copyright 2025 by Advent Mission Institute
                        </div>
                        <div>
                            <a href="privacy.html">Privacy Policy</a> <br>   
                        </div>
                    </div>
                </div>
            </div>
            
        </footer>

    </div>
    <!--End pagewrapper-->

    <a href="#" data-target="html" class="scroll-to-target scroll-to-top"><i class="fa fa-angle-up"></i></a>

    <!-- Custom Mobile Header Fix -->
    <style>
        @media (max-width: 991px) {
            .mobile-nav-toggler {
                margin-left: auto !important;
                margin-right: 0 !important;
                margin-top: 0 !important;
            }

            .nav-outer {
                justify-content: flex-end !important;
            }

            .header-upper .inner-container {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
            }

            .logo-box {
                flex: 1 !important;
            }

            .other-links {
                display: none !important;
            }
        }

        /* Video Background Styles */
        .slide-item {
            position: relative;
            overflow: hidden;
        }

        .video-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
        }

        .video-layer video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            position: absolute;
            top: 0;
            left: 0;
        }

        /* Ensure content is above video */
        .slide-item .auto-container {
            position: relative;
            z-index: 3;
        }

        .slide-item .left-top-line,
        .slide-item .right-bottom-curve,
        .slide-item .right-top-curve {
            position: relative;
            z-index: 2;
        }

        /* Mobile video optimization - can be controlled via JavaScript if needed */

        /* Fallback for browsers that don't support video */
        @supports not (object-fit: cover) {
            .video-layer {
                background-image: url(https://www.clearview.org/wp-content/uploads/2023/05/bible-study.jpeg);
                background-size: cover;
                background-position: center;
            }

            .video-layer video {
                display: none;
            }
        }
    </style>


    <script src="js/jquery.js"></script>
    <script src="js/popper.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/TweenMax.js"></script>
    <script src="js/jquery-ui.js"></script>
    <script src="js/jquery.fancybox.js"></script>
    <script src="js/owl.js"></script>
    <script src="js/mixitup.js"></script>
    <script src="js/knob.js"></script>
    <script src="js/validate.js"></script>
    <script src="js/appear.js"></script>
    <script src="js/wow.js"></script>
    <script src="js/jQuery.style.switcher.min.js"></script>
    <script type="text/javascript" src="../../cdnjs.cloudflare.com/ajax/libs/js-cookie/2.1.2/js.cookie.min.js">
    </script>
    <script src="js/jquery.easing.min.js"></script>
    <script src="js/custom-script.js"></script>


    <script src="js/lang.js"></script>
    <script src="../../translate.google.com/translate_a/elementa0d8.js?cb=googleTranslateElementInit"></script>
    <script src="js/color-switcher.js"></script>

    <!-- AMI Modal JavaScript -->
    <script>
        // Function to show modal with animation
        function showModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'flex';
                setTimeout(() => {
                    modal.classList.add('show');
                }, 10);
            }
        }

        // Function to close modal with animation
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 300);
            }
        }

        // Close modal when clicking on overlay
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('ami-modal-overlay')) {
                const modal = e.target.closest('.ami-modal');
                if (modal) {
                    closeModal(modal.id);
                }
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.ami-modal.show');
                if (openModal) {
                    closeModal(openModal.id);
                }
            }
        });

        // Enhanced form submission handling
        document.addEventListener('DOMContentLoaded', function() {
            const contactForm = document.getElementById('contact-form');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Get form data
                    const formData = new FormData(this);

                    // Show loading state (optional)
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span class="btn-title">Sending...</span>';
                    submitBtn.disabled = true;

                    // Check if fetch is supported, otherwise use XMLHttpRequest
                    if (typeof fetch !== 'undefined') {
                        // Modern browsers - use fetch
                        fetch('sendmail.php', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => {
                            // Reset button state
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;

                            if (response.ok) {
                                // Show success modal
                                showModal('successModal');
                                // Reset form
                                this.reset();
                            } else {
                                // Show error modal
                                showModal('errorModal');
                            }
                        })
                        .catch(error => {
                            // Reset button state
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;

                            // Show error modal
                            showModal('errorModal');
                            console.error('Form submission error:', error);
                        });
                    } else {
                        // Fallback for older browsers - use XMLHttpRequest
                        const xhr = new XMLHttpRequest();
                        xhr.open('POST', 'sendmail.php', true);

                        xhr.onreadystatechange = function() {
                            if (xhr.readyState === 4) {
                                // Reset button state
                                submitBtn.innerHTML = originalText;
                                submitBtn.disabled = false;

                                if (xhr.status === 200) {
                                    // Show success modal
                                    showModal('successModal');
                                    // Reset form
                                    contactForm.reset();
                                } else {
                                    // Show error modal
                                    showModal('errorModal');
                                }
                            }
                        };

                        xhr.onerror = function() {
                            // Reset button state
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;

                            // Show error modal
                            showModal('errorModal');
                        };

                        xhr.send(formData);
                    }
                });
            }
        });

        // Demo functions for testing (remove in production)
        function testSuccessModal() {
            showModal('successModal');
        }

        function testErrorModal() {
            showModal('errorModal');
        }
    </script>

    <!-- Custom Mobile Header Fix -->
    <style>
        @media (max-width: 991px) {
            .mobile-nav-toggler {
                margin-left: auto !important;
                margin-right: 0 !important;
            }
            .nav-outer {
                justify-content: flex-end !important;
            }
            .header-upper .inner-container {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
            }
            .logo-box {
                flex: 1 !important;
            }
            .other-links {
                display: none !important;
            }
        }

        /* AMI Modal Styles */
        .ami-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .ami-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .ami-modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(44, 62, 80, 0.8);
            backdrop-filter: blur(5px);
        }

        .ami-modal-content {
            position: relative;
            background: #ffffff;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(44, 62, 80, 0.3);
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow: hidden;
            transform: scale(0.7) translateY(50px);
            transition: all 0.3s ease;
        }

        .ami-modal.show .ami-modal-content {
            transform: scale(1) translateY(0);
        }

        .ami-modal-header {
            text-align: center;
            padding: 40px 30px 20px;
            background: linear-gradient(135deg, #87CEEB 0%, #B0E0E6 100%);
            color: #2C3E50;
        }

        .ami-modal-header.error {
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
            color: #ffffff;
        }

        .ami-success-icon,
        .ami-error-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            animation: iconPulse 2s infinite;
        }

        .ami-modal-header.error .ami-error-icon {
            background: rgba(255, 255, 255, 0.3);
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .ami-modal-header h3 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
            font-family: var(--thm-font);
        }

        .ami-modal-body {
            padding: 30px;
            text-align: center;
        }

        .ami-modal-body p {
            margin: 0;
            font-size: 16px;
            line-height: 1.6;
            color: #5A6C7D;
            font-family: var(--thm-b-font);
        }

        .ami-modal-footer {
            padding: 20px 30px 30px;
            text-align: center;
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .ami-btn-close,
        .ami-btn-retry {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            font-family: var(--thm-font);
            text-transform: uppercase;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
            justify-content: center;
        }

        .ami-btn-close {
            background: #f8f9fa;
            color: #5A6C7D;
            border: 2px solid #e9ecef;
        }

        .ami-btn-close:hover {
            background: #e9ecef;
            color: #2C3E50;
            transform: translateY(-2px);
        }

        .ami-btn-retry {
            background: #87CEEB;
            color: #2C3E50;
            border: 2px solid #87CEEB;
        }

        .ami-btn-retry:hover {
            background: #2C3E50;
            color: #ffffff;
            transform: translateY(-2px);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .ami-modal-content {
                width: 95%;
                margin: 20px;
            }

            .ami-modal-header {
                padding: 30px 20px 15px;
            }

            .ami-success-icon,
            .ami-error-icon {
                width: 60px;
                height: 60px;
                font-size: 30px;
                margin-bottom: 15px;
            }

            .ami-modal-header h3 {
                font-size: 24px;
            }

            .ami-modal-body {
                padding: 20px;
            }

            .ami-modal-footer {
                padding: 15px 20px 25px;
                flex-direction: column;
            }

            .ami-btn-close,
            .ami-btn-retry {
                width: 100%;
            }
        }
    </style>

</body>


<!-- Mirrored from pixydrops.com/linoorhtml/contact-2.html by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 29 May 2025 16:25:33 GMT -->

</html>